import { useState, useEffect, useRef } from 'react'
import { Nostalgist } from 'nostalgist'
import styled from 'styled-components'

const EmulatorContainer = styled.div`
  width: 100%;
  height: 500px;
  background: #000;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
`

const EmulatorCanvas = styled.div`
  width: 100%;
  height: 100%;
  
  canvas {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`

const LoadingMessage = styled.div`
  color: white;
  font-size: 1.2rem;
  text-align: center;
  
  .spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`

const ErrorMessage = styled.div`
  color: #ff6b6b;
  font-size: 1.1rem;
  text-align: center;
  padding: 20px;
  
  .error-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
  }
`

const PlaceholderMessage = styled.div`
  color: rgba(255,255,255,0.6);
  font-size: 1.1rem;
  text-align: center;
  
  .game-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
  }
`

function GameEmulator({ romFile, onEmulatorReady, onGameStateChange }) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [nostalgist, setNostalgist] = useState(null)
  const containerRef = useRef(null)

  useEffect(() => {
    if (romFile) {
      loadGame(romFile)
    }
  }, [romFile])

  // 清理函数
  useEffect(() => {
    return () => {
      if (nostalgist) {
        nostalgist.exit()
      }
    }
  }, [nostalgist])

  const loadGame = async (file) => {
    setLoading(true)
    setError(null)
    onGameStateChange(false)

    try {
      // 清理之前的实例
      if (nostalgist) {
        await nostalgist.exit()
      }

      // 检测ROM文件类型
      const fileName = file.name.toLowerCase()
      let core = 'fceumm' // 默认NES核心
      
      if (fileName.endsWith('.sfc') || fileName.endsWith('.smc')) {
        core = 'snes9x'
      } else if (fileName.endsWith('.gb') || fileName.endsWith('.gbc')) {
        core = 'gambatte'
      } else if (fileName.endsWith('.gba')) {
        core = 'mgba'
      } else if (fileName.endsWith('.md') || fileName.endsWith('.gen')) {
        core = 'genesis_plus_gx'
      }

      // 将文件转换为ArrayBuffer
      const arrayBuffer = await file.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)

      // 确保容器元素存在
      if (!containerRef.current) {
        throw new Error('模拟器容器未准备就绪')
      }

      // 清空容器内容
      containerRef.current.innerHTML = ''

      // 启动模拟器
      const nostalgistInstance = await Nostalgist.launch({
        core,
        rom: uint8Array,
        element: containerRef.current,
        retroarchConfig: {
          rewind_enable: true,
          savestate_auto_save: true,
          savestate_auto_load: true,
        }
      })

      setNostalgist(nostalgistInstance)
      onEmulatorReady(nostalgistInstance)
      onGameStateChange(true)
      setLoading(false)

    } catch (err) {
      console.error('加载游戏失败:', err)
      console.error('错误详情:', {
        message: err.message,
        stack: err.stack,
        containerRef: containerRef.current,
        romFile: file?.name
      })
      setError(`加载游戏失败: ${err.message}`)
      setLoading(false)
      onGameStateChange(false)
    }
  }

  const renderContent = () => {
    if (loading) {
      return (
        <LoadingMessage>
          <div className="spinner"></div>
          正在加载游戏...
        </LoadingMessage>
      )
    }

    if (error) {
      return (
        <ErrorMessage>
          <span className="error-icon">⚠️</span>
          {error}
        </ErrorMessage>
      )
    }

    if (!romFile) {
      return (
        <PlaceholderMessage>
          <span className="game-icon">🎮</span>
          请上传ROM文件开始游戏
        </PlaceholderMessage>
      )
    }

    return null
  }

  return (
    <EmulatorContainer>
      {!nostalgist && renderContent()}
      <EmulatorCanvas ref={containerRef} />
    </EmulatorContainer>
  )
}

export default GameEmulator
