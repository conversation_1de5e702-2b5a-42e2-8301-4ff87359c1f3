import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // 开发服务器配置
  server: {
    port: 5173,
    host: true, // 允许外部访问
    open: true  // 自动打开浏览器
  },

  // 构建配置
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',

    // 代码分割配置
    rollupOptions: {
      output: {
        manualChunks: {
          // 将React相关库分离到vendor chunk
          vendor: ['react', 'react-dom'],
          // 将模拟器引擎分离
          emulator: ['nostalgist'],
          // 将UI库分离
          ui: ['styled-components', 'react-dropzone']
        }
      }
    },

    // Terser压缩配置
    terserOptions: {
      compress: {
        drop_console: true,  // 移除console.log
        drop_debugger: true  // 移除debugger
      }
    }
  },

  // 优化配置
  optimizeDeps: {
    include: ['react', 'react-dom', 'styled-components', 'react-dropzone'],
    exclude: ['nostalgist'] // 排除模拟器库，避免预构建问题
  },

  // WebAssembly 支持配置
  worker: {
    format: 'es'
  },

  // 确保正确处理 .wasm 文件
  assetsInclude: ['**/*.wasm']
})
